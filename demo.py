#!/usr/bin/env python3
"""
Демонстрация парсера документации
"""
import asyncio
import json
from pathlib import Path

from parsers.base_parser import DocumentationParser
from utils.exporter import DataExporter
from config import BASE_URL


async def demo_parser():
    """Демонстрация работы парсера"""
    print("🚀 Демонстрация парсера документации")
    print("=" * 50)
    
    # URL для демонстрации
    demo_url = "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager"
    
    print(f"📍 Парсинг: {demo_url}")
    print(f"🏠 Базовый URL: {BASE_URL}")
    print(f"📊 Глубина: 0 (только одна страница)")
    print()
    
    # Создание директории для демо
    demo_dir = Path("demo_output")
    demo_dir.mkdir(exist_ok=True)
    
    try:
        # Инициализация парсера
        async with DocumentationParser(BASE_URL, demo_url, max_depth=0) as parser:
            print("🔄 Запуск парсинга...")
            
            # Парсинг
            result = await parser.crawl_recursively()
            
            print(f"✅ Парсинг завершен!")
            print(f"📄 Страниц обработано: {result.total_pages}")
            print(f"✅ Успешно: {result.success_count}")
            print(f"❌ Ошибок: {result.error_count}")
            print(f"⏱️  Время: {result.duration:.2f} секунд")
            print()
            
            if result.pages:
                page = result.pages[0]
                print("📋 Информация о странице:")
                print(f"   🏷️  Заголовок: {page.title}")
                print(f"   🔗 URL: {page.url}")
                print(f"   📝 Описание: {page.description or 'Не найдено'}")
                print(f"   🔧 Методов: {len(page.methods)}")
                print(f"   📊 Свойств: {len(page.properties)}")
                print(f"   💻 Примеров кода: {len(page.examples)}")
                print(f"   🔗 Связанных ссылок: {len(page.related_links)}")
                print()
                
                # Показ примеров кода
                if page.examples:
                    print("💻 Примеры кода:")
                    for i, example in enumerate(page.examples[:3], 1):
                        print(f"   {i}. Язык: {example.language}")
                        print(f"      Код: {example.code[:50]}...")
                    print()
                
                # Показ связанных ссылок
                if page.related_links:
                    print("🔗 Связанные ссылки (первые 5):")
                    for i, link in enumerate(page.related_links[:5], 1):
                        print(f"   {i}. {link}")
                    print()
            
            # Экспорт результатов
            print("💾 Экспорт результатов...")
            exporter = DataExporter(demo_dir)
            export_stats = await exporter.export_all(result)
            
            print(f"📝 Markdown файлов: {export_stats['exported_markdown']}")
            print(f"📋 JSON файлов: {export_stats['exported_json']}")
            
            if export_stats['errors']:
                print(f"⚠️  Ошибки экспорта: {len(export_stats['errors'])}")
            
            print()
            print("📁 Результаты сохранены в:")
            print(f"   📝 Markdown: {demo_dir / 'markdown'}")
            print(f"   📋 JSON: {demo_dir / 'json'}")
            print(f"   📊 Отчеты: {demo_dir}")
            
            # Показ содержимого JSON файла
            json_files = list((demo_dir / "json").glob("*.json"))
            if json_files:
                print()
                print("📋 Пример JSON структуры:")
                with open(json_files[0], 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Показываем только основные поля
                    demo_data = {
                        "url": data.get("url"),
                        "title": data.get("title"),
                        "methods_count": len(data.get("methods", [])),
                        "properties_count": len(data.get("properties", [])),
                        "examples_count": len(data.get("examples", [])),
                        "related_links_count": len(data.get("related_links", []))
                    }
                    print(json.dumps(demo_data, indent=2, ensure_ascii=False))
    
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return False
    
    print()
    print("🎉 Демонстрация завершена успешно!")
    return True


if __name__ == "__main__":
    asyncio.run(demo_parser())
