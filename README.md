# Documentation Parser

Мощный парсер технической документации с поддержкой JavaScript, созданный для извлечения структурированной информации из веб-сайтов документации и преобразования её в формат, пригодный для ИИ-агентов.

## 🚀 Возможности

- **Поддержка JavaScript**: Использует Playwright для полной загрузки динамического контента
- **Рекурсивный обход**: Автоматически находит и обходит связанные страницы документации
- **Интеллектуальная очистка**: Удаляет ненужные элементы (навигация, реклама, футеры)
- **Структурированное извлечение**: Извлекает классы, методы, параметры, примеры кода
- **Множественные форматы**: Экспорт в Markdown и JSON
- **Параллельная обработка**: Поддержка многопоточного парсинга
- **Подробная отчетность**: Генерация сводных отчетов и статистики

## 📦 Установка

1. Клонируйте репозиторий:
```bash
git clone <repository-url>
cd Parser
```

2. Установите зависимости:
```bash
pip install -r requirements.txt
```

3. Установите браузеры для Playwright:
```bash
playwright install
```

## 🎯 Быстрый старт

### Базовое использование

```bash
# Парсинг документации Apple Core Bluetooth
python main.py parse

# Парсинг с пользовательским URL
python main.py parse --url "https://developer.apple.com/documentation/foundation"

# Параллельный режим с 10 потоками
python main.py parse --mode concurrent --concurrent 10
```

### Расширенные опции

```bash
# Полная настройка
python main.py parse \
  --url "https://developer.apple.com/documentation/corebluetooth" \
  --base-url "https://developer.apple.com/documentation" \
  --max-depth 3 \
  --output "./my_results" \
  --delay 2 \
  --concurrent 5 \
  --mode concurrent \
  --verbose
```

## 📋 Команды CLI

### `parse` - Основная команда парсинга

```bash
python main.py parse [OPTIONS]
```

**Опции:**
- `--url, -u`: Стартовый URL для парсинга
- `--base-url, -b`: Базовый URL сайта
- `--max-depth, -d`: Максимальная глубина обхода (по умолчанию: 3)
- `--output, -o`: Директория для сохранения результатов
- `--delay`: Задержка между запросами в секундах
- `--concurrent, -c`: Количество одновременных запросов
- `--mode`: Режим парсинга (`sequential` или `concurrent`)
- `--verbose, -v`: Подробный вывод

### `stats` - Статистика результатов

```bash
python main.py stats --output "./output"
```

### `clean` - Очистка результатов

```bash
python main.py clean --output "./output"
```

## 📁 Структура результатов

После парсинга создается следующая структура:

```
output/
├── markdown/           # Markdown файлы для каждой страницы
│   ├── index.md       # Индекс всех страниц
│   ├── cbcentralmanager.md
│   └── ...
├── json/              # JSON файлы с структурированными данными
│   ├── cbcentralmanager.json
│   └── ...
├── logs/              # Логи парсинга
│   └── parser.log
├── parsing_report.md  # Сводный отчет в Markdown
└── parsing_report.json # Сводный отчет в JSON
```

## 📄 Формат данных

### Markdown пример

```markdown
# Class: CBCentralManager

## Description
Объект для управления и выполнения Bluetooth Low Energy центральной роли.

## Properties

### state
- **Type:** `CBManagerState`
- **Description:** Текущее состояние центрального менеджера
- **Access:** Read-only

## Methods

### scanForPeripherals(withServices:options:)
Сканирует периферийные устройства Bluetooth LE.

**Parameters:**
- `withServices` (Array<CBUUID>?): Массив UUID сервисов для поиска
- `options` (Dictionary?): Дополнительные опции сканирования

**Example (swift):**
```swift
centralManager.scanForPeripherals(withServices: [serviceUUID], options: nil)
```
```

### JSON пример

```json
{
  "url": "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager",
  "title": "CBCentralManager",
  "class_name": "CBCentralManager",
  "description": "Объект для управления и выполнения Bluetooth Low Energy центральной роли.",
  "methods": [
    {
      "name": "scanForPeripherals(withServices:options:)",
      "description": "Сканирует периферийные устройства Bluetooth LE.",
      "parameters": [
        {
          "name": "withServices",
          "type": "Array<CBUUID>?",
          "description": "Массив UUID сервисов для поиска",
          "required": true
        }
      ],
      "examples": [
        {
          "language": "swift",
          "code": "centralManager.scanForPeripherals(withServices: [serviceUUID], options: nil)"
        }
      ]
    }
  ],
  "properties": [
    {
      "name": "state",
      "type": "CBManagerState",
      "description": "Текущее состояние центрального менеджера",
      "readonly": true
    }
  ]
}
```

## ⚙️ Конфигурация

Основные настройки находятся в файле `config.py`:

```python
# Базовые настройки
BASE_URL = "https://developer.apple.com/documentation"
START_URL = "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager"

# Настройки парсера
MAX_DEPTH = 3
DELAY_BETWEEN_REQUESTS = 1
MAX_CONCURRENT_REQUESTS = 5

# Селекторы для извлечения контента
SELECTORS = {
    "title": "h1.title, h1.page-title, .hero-title h1",
    "description": ".description, .summary, .abstract",
    "methods": ".method, .function, .property",
    # ...
}
```

## 🔧 Расширение функциональности

### Добавление нового парсера

1. Создайте новый класс, наследующий от `DocumentationParser`
2. Переопределите методы извлечения контента
3. Добавьте специфичные селекторы в конфигурацию

### Кастомизация очистки контента

Модифицируйте `utils/content_cleaner.py` для добавления новых правил очистки:

```python
class CustomContentCleaner(ContentCleaner):
    def __init__(self):
        super().__init__()
        self.remove_selectors.extend([
            '.custom-element',
            '.unwanted-section'
        ])
```

## 🐛 Отладка

Включите подробный режим для детальной информации:

```bash
python main.py parse --verbose
```

Логи сохраняются в `output/logs/parser.log`.

## 📊 Производительность

- **Последовательный режим**: Безопасный, медленный
- **Параллельный режим**: Быстрый, но может вызвать блокировку сервера
- **Рекомендуемые настройки**: 3-5 одновременных запросов с задержкой 1-2 секунды

## 🤝 Вклад в проект

1. Форкните репозиторий
2. Создайте ветку для новой функции
3. Внесите изменения
4. Добавьте тесты
5. Создайте Pull Request

## 📝 Лицензия

MIT License - см. файл LICENSE для деталей.

## 🆘 Поддержка

При возникновении проблем:

1. Проверьте логи в `output/logs/parser.log`
2. Убедитесь, что все зависимости установлены
3. Попробуйте уменьшить количество одновременных запросов
4. Создайте issue с подробным описанием проблемы
