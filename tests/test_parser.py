"""
Тесты для парсера документации
"""
import pytest
import asyncio
from pathlib import Path
from bs4 import BeautifulSoup

from parsers.base_parser import DocumentationParser
from parsers.content_extractor import ContentExtractor
from utils.content_cleaner import ContentCleaner
from utils.exporter import DataExporter
from models import DocumentationPage, Method, Property, Parameter, CodeExample


class TestContentExtractor:
    """Тесты для извлечения контента"""
    
    def setup_method(self):
        self.extractor = ContentExtractor()
    
    def test_extract_methods_from_html(self):
        """Тест извлечения методов из HTML"""
        html = """
        <div class="method">
            <h3 class="method-name">scanForPeripherals</h3>
            <p class="description">Scans for BLE devices</p>
            <div class="parameters">
                <div class="parameter">
                    <span>withServices: Array&lt;CBUUID&gt;</span>
                    <p>Array of service UUIDs</p>
                </div>
            </div>
        </div>
        """
        soup = BeautifulSoup(html, 'lxml')
        methods = self.extractor.extract_methods(soup)
        
        assert len(methods) > 0
        assert methods[0].name == "scanForPeripherals"
        assert "Scans for BLE devices" in methods[0].description
    
    def test_extract_properties_from_html(self):
        """Тест извлечения свойств из HTML"""
        html = """
        <div class="property">
            <h4 class="property-name">state</h4>
            <p class="description">Current state of the manager</p>
            <span class="type">CBManagerState</span>
        </div>
        """
        soup = BeautifulSoup(html, 'lxml')
        properties = self.extractor.extract_properties(soup)
        
        assert len(properties) > 0
        assert properties[0].name == "state"
    
    def test_extract_code_examples(self):
        """Тест извлечения примеров кода"""
        html = """
        <pre><code class="swift">
        centralManager.scanForPeripherals(withServices: [serviceUUID], options: nil)
        </code></pre>
        """
        soup = BeautifulSoup(html, 'lxml')
        examples = self.extractor.extract_code_examples(soup)
        
        assert len(examples) > 0
        assert examples[0].language == "swift"
        assert "scanForPeripherals" in examples[0].code


class TestContentCleaner:
    """Тесты для очистки контента"""
    
    def setup_method(self):
        self.cleaner = ContentCleaner()
    
    def test_remove_navigation_elements(self):
        """Тест удаления навигационных элементов"""
        html = """
        <html>
            <body>
                <nav class="navigation">Navigation</nav>
                <main class="content">
                    <h1>Title</h1>
                    <p>Content</p>
                </main>
                <footer>Footer</footer>
            </body>
        </html>
        """
        soup = BeautifulSoup(html, 'lxml')
        cleaned = self.cleaner.clean_html(soup)
        
        # Навигация и футер должны быть удалены
        assert not cleaned.find('nav')
        assert not cleaned.find('footer')
        
        # Основной контент должен остаться
        assert cleaned.find('h1')
        assert cleaned.find('p')
    
    def test_remove_empty_elements(self):
        """Тест удаления пустых элементов"""
        html = """
        <div>
            <p></p>
            <div>   </div>
            <span>Content</span>
            <div><p></p></div>
        </div>
        """
        soup = BeautifulSoup(html, 'lxml')
        cleaned = self.cleaner.clean_html(soup)
        
        # Пустые элементы должны быть удалены
        empty_elements = cleaned.find_all(['p', 'div'], string=lambda text: not text or not text.strip())
        assert len(empty_elements) == 0
        
        # Элемент с контентом должен остаться
        assert cleaned.find('span', string='Content')


class TestDataExporter:
    """Тесты для экспорта данных"""
    
    def setup_method(self):
        self.test_dir = Path("test_output")
        self.test_dir.mkdir(exist_ok=True)
        self.exporter = DataExporter(self.test_dir)
    
    def teardown_method(self):
        """Очистка тестовых файлов"""
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    @pytest.mark.asyncio
    async def test_export_page_to_markdown(self):
        """Тест экспорта страницы в Markdown"""
        page = DocumentationPage(
            url="https://example.com/test",
            title="Test Class",
            class_name="TestClass",
            description="Test description",
            methods=[
                Method(
                    name="testMethod",
                    description="Test method description",
                    parameters=[
                        Parameter(name="param1", type="String", description="Test parameter")
                    ]
                )
            ]
        )
        
        await self.exporter.export_page_to_markdown(page)
        
        # Проверяем, что файл создан
        markdown_files = list(self.exporter.markdown_dir.glob("*.md"))
        assert len(markdown_files) > 0
        
        # Проверяем содержимое
        with open(markdown_files[0], 'r', encoding='utf-8') as f:
            content = f.read()
            assert "# Class: TestClass" in content
            assert "testMethod" in content
            assert "param1" in content
    
    @pytest.mark.asyncio
    async def test_export_page_to_json(self):
        """Тест экспорта страницы в JSON"""
        page = DocumentationPage(
            url="https://example.com/test",
            title="Test Class",
            description="Test description"
        )
        
        await self.exporter.export_page_to_json(page)
        
        # Проверяем, что файл создан
        json_files = list(self.exporter.json_dir.glob("*.json"))
        assert len(json_files) > 0
        
        # Проверяем содержимое
        import json
        with open(json_files[0], 'r', encoding='utf-8') as f:
            data = json.load(f)
            assert data['title'] == "Test Class"
            assert data['url'] == "https://example.com/test"


class TestDocumentationParser:
    """Тесты для основного парсера"""
    
    def test_url_validation(self):
        """Тест валидации URL"""
        parser = DocumentationParser(
            "https://developer.apple.com/documentation",
            "https://developer.apple.com/documentation/corebluetooth"
        )
        
        # Валидные URL
        assert parser.is_valid_url("https://developer.apple.com/documentation/corebluetooth/cbcentral")
        assert parser.is_valid_url("https://developer.apple.com/documentation/foundation/nsstring")
        
        # Невалидные URL
        assert not parser.is_valid_url("https://google.com")
        assert not parser.is_valid_url("https://developer.apple.com/tutorials/")
        assert not parser.is_valid_url("")
    
    def test_title_extraction(self):
        """Тест извлечения заголовков"""
        parser = DocumentationParser("", "")
        
        html = """
        <html>
            <body>
                <h1 class="title">CBCentralManager</h1>
                <p>Description</p>
            </body>
        </html>
        """
        soup = BeautifulSoup(html, 'lxml')
        title = parser.extract_title(soup)
        
        assert title == "CBCentralManager"


def test_models_validation():
    """Тест валидации моделей данных"""
    # Тест создания метода
    method = Method(
        name="testMethod",
        description="Test description",
        parameters=[
            Parameter(name="param1", type="String", required=True)
        ],
        return_type="Bool"
    )
    
    assert method.name == "testMethod"
    assert len(method.parameters) == 1
    assert method.parameters[0].required == True
    
    # Тест создания страницы
    page = DocumentationPage(
        url="https://example.com",
        title="Test Page",
        methods=[method]
    )
    
    assert page.title == "Test Page"
    assert len(page.methods) == 1
    
    # Тест конвертации в Markdown
    markdown = page.to_markdown()
    assert "# Test Page" in markdown
    assert "testMethod" in markdown


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
