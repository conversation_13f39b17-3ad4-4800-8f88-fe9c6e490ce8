# API Reference

## Metadata
- **URL:** https://developer.apple.com/documentation/corebluetooth/cbcentralmanager
- **Parsed at:** 2025-07-01 01:08:02

## Code Examples

```unknown
classCBCentralManager
```

## Related Links

- [https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#overview](https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#overview)
- [https://developer.apple.com/documentation/corebluetooth/cbperipheral](https://developer.apple.com/documentation/corebluetooth/cbperipheral)
- [https://developer.apple.com/documentation/corebluetooth/cbcentralmanagerstate/poweredon](https://developer.apple.com/documentation/corebluetooth/cbcentralmanagerstate/poweredon)
- [https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#topics](https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#topics)
- [https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Initializing-a-Central-Manager](https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Initializing-a-Central-Manager)
- [https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/init()](https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/init())
- [https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/init(delegate:queue:)](https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/init(delegate:queue:))
- [https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/init(delegate:queue:options:)](https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/init(delegate:queue:options:))
- [https://developer.apple.com/documentation/corebluetooth/central-manager-initialization-options](https://developer.apple.com/documentation/corebluetooth/central-manager-initialization-options)
- [https://developer.apple.com/documentation/corebluetooth/central-manager-state-restoration-options](https://developer.apple.com/documentation/corebluetooth/central-manager-state-restoration-options)
