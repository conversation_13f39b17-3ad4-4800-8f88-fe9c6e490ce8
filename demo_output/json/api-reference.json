{"url": "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager", "title": "API Reference", "description": null, "class_name": null, "methods": [], "properties": [], "examples": [{"language": "unknown", "code": "classCBCentralManager", "description": null}], "related_links": ["https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#overview", "https://developer.apple.com/documentation/corebluetooth/cbperipheral", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanagerstate/poweredon", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#topics", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Initializing-a-Central-Manager", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/init()", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/init(delegate:queue:)", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/init(delegate:queue:options:)", "https://developer.apple.com/documentation/corebluetooth/central-manager-initialization-options", "https://developer.apple.com/documentation/corebluetooth/central-manager-state-restoration-options", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Establishing-or-Canceling-Connections-with-Peripherals", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/connect(_:options:)", "https://developer.apple.com/documentation/corebluetooth/peripheral-connection-options", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/cancelperipheralconnection(_:)", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Retrieving-Lists-of-Peripherals", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/retrieveconnectedperipherals(withservices:)", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/retrieveperipherals(withidentifiers:)", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Scanning-or-Stopping-Scans-of-Peripherals", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/scanforperipherals(withservices:options:)", "https://developer.apple.com/documentation/corebluetooth/peripheral-scanning-options", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/stopscan()", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/isscanning", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Inspecting-Feature-Support", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/supports(_:)", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/feature", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Monitoring-Properties", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/delegate", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Receiving-Connection-Events", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager/registerforconnectionevents(options:)", "https://developer.apple.com/documentation/corebluetooth/peripheral-connection-options", "https://developer.apple.com/documentation/corebluetooth/cbconnectionevent", "https://developer.apple.com/documentation/corebluetooth/cbconnectioneventmatchingoption", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Deprecated", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanagerstate", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#relationships", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#inherits-from", "https://developer.apple.com/documentation/corebluetooth/cbmanager", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#conforms-to", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#see-also", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager#Centrals", "https://developer.apple.com/documentation/corebluetooth/cbcentral", "https://developer.apple.com/documentation/corebluetooth/cbcentralmanagerdelegate"], "parsed_at": "2025-07-01T01:08:02.186513"}