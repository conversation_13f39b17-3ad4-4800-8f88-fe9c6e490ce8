#!/usr/bin/env python3
"""
Главный модуль парсера документации
"""
import asyncio
import logging
import sys
from pathlib import Path
from typing import Optional

import click
from colorama import init, Fore, Style
from tqdm import tqdm

# Инициализация colorama для Windows
init()

from parsers.base_parser import DocumentationParser
from utils.exporter import DataExporter
from config import (
    BASE_URL, START_URL, MAX_DEPTH, OUTPUT_DIR, 
    DELAY_BETWEEN_REQUESTS, MAX_CONCURRENT_REQUESTS
)


def setup_logging(verbose: bool = False):
    """Настройка логирования"""
    level = logging.DEBUG if verbose else logging.INFO
    
    # Настройка форматирования
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Консольный обработчик
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # Файловый обработчик
    log_file = OUTPUT_DIR / "logs" / "parser.log"
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    
    # Настройка корневого логгера
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)


def print_banner():
    """Вывод баннера приложения"""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    Documentation Parser                      ║
║                                                              ║
║  Парсер технической документации с поддержкой JavaScript    ║
║  Экспорт в Markdown и JSON для ИИ-агентов                   ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(banner)


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Подробный вывод')
@click.pass_context
def cli(ctx, verbose):
    """Парсер документации для ИИ-агентов"""
    ctx.ensure_object(dict)
    ctx.obj['verbose'] = verbose
    setup_logging(verbose)


@cli.command()
@click.option('--url', '-u', default=START_URL, help='Стартовый URL для парсинга')
@click.option('--base-url', '-b', default=BASE_URL, help='Базовый URL сайта')
@click.option('--max-depth', '-d', default=MAX_DEPTH, help='Максимальная глубина обхода')
@click.option('--output', '-o', default=str(OUTPUT_DIR), help='Директория для сохранения результатов')
@click.option('--delay', default=DELAY_BETWEEN_REQUESTS, help='Задержка между запросами (сек)')
@click.option('--concurrent', '-c', default=MAX_CONCURRENT_REQUESTS, help='Максимальное количество одновременных запросов')
@click.option('--mode', type=click.Choice(['sequential', 'concurrent']), default='sequential', help='Режим парсинга')
@click.pass_context
async def parse(ctx, url, base_url, max_depth, output, delay, concurrent, mode):
    """Запуск парсинга документации"""
    print_banner()
    
    logger = logging.getLogger(__name__)
    logger.info(f"Начало парсинга: {url}")
    
    # Создание директории вывода
    output_path = Path(output)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"{Fore.GREEN}🚀 Запуск парсера...{Style.RESET_ALL}")
    print(f"📍 Стартовый URL: {url}")
    print(f"🏠 Базовый URL: {base_url}")
    print(f"📊 Максимальная глубина: {max_depth}")
    print(f"💾 Директория вывода: {output_path}")
    print(f"⏱️  Задержка между запросами: {delay}с")
    print(f"🔄 Режим: {mode}")
    print()
    
    try:
        # Инициализация парсера
        async with DocumentationParser(base_url, url, max_depth) as parser:
            # Обновление конфигурации
            parser.delay = delay
            
            # Запуск парсинга
            if mode == 'concurrent':
                print(f"{Fore.YELLOW}⚡ Параллельный режим ({concurrent} потоков)...{Style.RESET_ALL}")
                result = await parser.parse_with_concurrency(concurrent)
            else:
                print(f"{Fore.YELLOW}🔄 Последовательный режим...{Style.RESET_ALL}")
                result = await parser.crawl_recursively()
            
            # Вывод статистики
            print(f"\n{Fore.GREEN}✅ Парсинг завершен!{Style.RESET_ALL}")
            print(f"📄 Обработано страниц: {result.total_pages}")
            print(f"✅ Успешно: {result.success_count}")
            print(f"❌ Ошибок: {result.error_count}")
            
            if result.duration:
                print(f"⏱️  Время выполнения: {result.duration:.2f} секунд")
            
            # Экспорт результатов
            print(f"\n{Fore.CYAN}💾 Экспорт результатов...{Style.RESET_ALL}")
            exporter = DataExporter(output_path)
            export_stats = await exporter.export_all(result)
            
            print(f"📝 Экспортировано Markdown файлов: {export_stats['exported_markdown']}")
            print(f"📋 Экспортировано JSON файлов: {export_stats['exported_json']}")
            
            if export_stats['errors']:
                print(f"{Fore.RED}⚠️  Ошибки экспорта: {len(export_stats['errors'])}{Style.RESET_ALL}")
                for error in export_stats['errors'][:5]:  # Показываем первые 5 ошибок
                    print(f"   - {error}")
            
            # Показ структуры результатов
            print(f"\n{Fore.GREEN}📁 Результаты сохранены в:{Style.RESET_ALL}")
            print(f"   📝 Markdown: {output_path / 'markdown'}")
            print(f"   📋 JSON: {output_path / 'json'}")
            print(f"   📊 Отчеты: {output_path}")
            
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}⏹️  Парсинг прерван пользователем{Style.RESET_ALL}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        print(f"\n{Fore.RED}💥 Критическая ошибка: {e}{Style.RESET_ALL}")
        sys.exit(1)


@cli.command()
@click.option('--output', '-o', default=str(OUTPUT_DIR), help='Директория с результатами')
def stats(output):
    """Показать статистику по результатам парсинга"""
    output_path = Path(output)
    
    if not output_path.exists():
        print(f"{Fore.RED}❌ Директория {output_path} не найдена{Style.RESET_ALL}")
        return
    
    markdown_dir = output_path / "markdown"
    json_dir = output_path / "json"
    
    markdown_files = list(markdown_dir.glob("*.md")) if markdown_dir.exists() else []
    json_files = list(json_dir.glob("*.json")) if json_dir.exists() else []
    
    print(f"{Fore.CYAN}📊 Статистика результатов парсинга{Style.RESET_ALL}")
    print(f"📁 Директория: {output_path}")
    print(f"📝 Markdown файлов: {len(markdown_files)}")
    print(f"📋 JSON файлов: {len(json_files)}")
    
    # Проверка отчетов
    report_md = output_path / "parsing_report.md"
    report_json = output_path / "parsing_report.json"
    
    if report_md.exists():
        print(f"📊 Отчет Markdown: ✅")
    if report_json.exists():
        print(f"📊 Отчет JSON: ✅")
    
    # Показ последних файлов
    if markdown_files:
        print(f"\n{Fore.GREEN}📝 Последние Markdown файлы:{Style.RESET_ALL}")
        for file in sorted(markdown_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]:
            print(f"   - {file.name}")
    
    if json_files:
        print(f"\n{Fore.GREEN}📋 Последние JSON файлы:{Style.RESET_ALL}")
        for file in sorted(json_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]:
            print(f"   - {file.name}")


@cli.command()
@click.option('--output', '-o', default=str(OUTPUT_DIR), help='Директория для очистки')
@click.confirmation_option(prompt='Вы уверены, что хотите удалить все результаты?')
def clean(output):
    """Очистка результатов парсинга"""
    import shutil
    
    output_path = Path(output)
    
    if output_path.exists():
        shutil.rmtree(output_path)
        print(f"{Fore.GREEN}✅ Директория {output_path} очищена{Style.RESET_ALL}")
    else:
        print(f"{Fore.YELLOW}⚠️  Директория {output_path} не существует{Style.RESET_ALL}")


def main():
    """Главная функция"""
    # Обертка для async команд
    def async_command(f):
        def wrapper(*args, **kwargs):
            return asyncio.run(f(*args, **kwargs))
        return wrapper
    
    # Применяем обертку к async командам
    cli.commands['parse'].callback = async_command(cli.commands['parse'].callback)
    
    cli()


if __name__ == "__main__":
    main()
