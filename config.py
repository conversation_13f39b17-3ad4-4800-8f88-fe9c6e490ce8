"""
Конфигурация для парсера документации
"""
import os
from pathlib import Path

# Базовые настройки
BASE_URL = "https://developer.apple.com/documentation"
START_URL = "https://developer.apple.com/documentation/corebluetooth/cbcentralmanager"

# Настройки парсера
MAX_DEPTH = 3  # Максимальная глубина рекурсивного обхода
DELAY_BETWEEN_REQUESTS = 1  # Задержка между запросами в секундах
MAX_CONCURRENT_REQUESTS = 5  # Максимальное количество одновременных запросов

# Пути для сохранения
OUTPUT_DIR = Path("output")
MARKDOWN_DIR = OUTPUT_DIR / "markdown"
JSON_DIR = OUTPUT_DIR / "json"
LOGS_DIR = OUTPUT_DIR / "logs"

# Создание директорий
for dir_path in [OUTPUT_DIR, MARKDOWN_DIR, JSON_DIR, LOGS_DIR]:
    dir_path.mkdir(exist_ok=True)

# Настройки браузера
BROWSER_CONFIG = {
    "headless": True,
    "viewport": {"width": 1920, "height": 1080},
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}

# Селекторы для извлечения контента
SELECTORS = {
    "title": "h1.title, h1.page-title, .hero-title h1",
    "description": ".description, .summary, .abstract",
    "methods": ".method, .function, .property",
    "parameters": ".parameter, .param",
    "code_examples": "pre code, .code-listing, .highlight",
    "navigation_links": "a[href*='/documentation/']",
    "content_area": "main, .main-content, .content",
    "remove_elements": [
        "nav", "header", "footer", ".sidebar", ".navigation", 
        ".breadcrumb", ".page-nav", ".feedback", ".related-links",
        ".advertisement", ".banner", ".cookie-notice"
    ]
}

# Фильтры URL
URL_FILTERS = {
    "include_patterns": [
        r"/documentation/corebluetooth/",
        r"/documentation/foundation/",
        r"/documentation/swift/"
    ],
    "exclude_patterns": [
        r"/tutorials/",
        r"/sample-code/",
        r"/videos/",
        r"/forums/",
        r"/news/"
    ]
}
