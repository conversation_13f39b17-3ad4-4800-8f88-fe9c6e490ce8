"""
Утилиты для очистки контента от ненужных элементов
"""
import re
from typing import List, Set
from bs4 import BeautifulSoup, Tag, NavigableString, Comment


class ContentCleaner:
    """Класс для очистки HTML контента"""
    
    def __init__(self):
        # Селекторы элементов для удаления
        self.remove_selectors = [
            # Навигация и меню
            'nav', 'header', 'footer', '.navigation', '.nav', '.menu',
            '.breadcrumb', '.breadcrumbs', '.page-nav', '.site-nav',
            
            # Боковые панели
            '.sidebar', '.side-nav', '.aside', 'aside', '.secondary',
            
            # Реклама и промо
            '.advertisement', '.ads', '.promo', '.banner', '.promotion',
            '.marketing', '.cta', '.call-to-action',
            
            # Социальные сети и обратная связь
            '.social', '.share', '.feedback', '.rating', '.comments',
            '.discussion', '.forum-link',
            
            # Уведомления и алерты
            '.alert', '.notice', '.notification', '.cookie-notice',
            '.privacy-notice', '.gdpr', '.consent',
            
            # Связанные ссылки и рекомендации
            '.related-links', '.see-also', '.recommendations', '.suggested',
            '.more-info', '.additional-resources',
            
            # Метаданные и служебная информация
            '.metadata', '.tags', '.categories', '.last-updated',
            '.edit-link', '.source-link', '.github-link',
            
            # Поиск и фильтры
            '.search', '.filter', '.sort', '.pagination',
            
            # Специфичные для Apple Developer
            '.developer-nav', '.global-nav', '.local-nav',
            '.supplementary', '.complement', '.aside-content',
            '.page-metadata', '.page-tags'
        ]
        
        # Атрибуты для удаления
        self.remove_attributes = [
            'onclick', 'onload', 'onmouseover', 'onmouseout',
            'style', 'class', 'id', 'data-*'
        ]
        
        # Классы, указывающие на ненужный контент
        self.unwanted_classes = [
            'hidden', 'invisible', 'sr-only', 'screen-reader',
            'skip-link', 'accessibility', 'a11y'
        ]
        
        # Паттерны для очистки текста
        self.text_cleanup_patterns = [
            (r'\s+', ' '),  # Множественные пробелы
            (r'\n\s*\n\s*\n', '\n\n'),  # Множественные переносы строк
            (r'^\s+|\s+$', ''),  # Пробелы в начале и конце
        ]
    
    def clean_html(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Основная функция очистки HTML"""
        # Создаем копию для безопасности
        cleaned_soup = BeautifulSoup(str(soup), 'lxml')
        
        # Удаление ненужных элементов
        self._remove_unwanted_elements(cleaned_soup)
        
        # Удаление комментариев
        self._remove_comments(cleaned_soup)
        
        # Удаление скриптов и стилей
        self._remove_scripts_and_styles(cleaned_soup)
        
        # Очистка атрибутов
        self._clean_attributes(cleaned_soup)
        
        # Удаление пустых элементов
        self._remove_empty_elements(cleaned_soup)
        
        # Нормализация текста
        self._normalize_text(cleaned_soup)
        
        return cleaned_soup
    
    def _remove_unwanted_elements(self, soup: BeautifulSoup):
        """Удаление ненужных элементов по селекторам"""
        for selector in self.remove_selectors:
            elements = soup.select(selector)
            for element in elements:
                element.decompose()
    
    def _remove_comments(self, soup: BeautifulSoup):
        """Удаление HTML комментариев"""
        comments = soup.find_all(string=lambda text: isinstance(text, Comment))
        for comment in comments:
            comment.extract()
    
    def _remove_scripts_and_styles(self, soup: BeautifulSoup):
        """Удаление скриптов и стилей"""
        for tag in soup(['script', 'style', 'noscript']):
            tag.decompose()
    
    def _clean_attributes(self, soup: BeautifulSoup):
        """Очистка атрибутов элементов"""
        for tag in soup.find_all(True):
            # Сохраняем только важные атрибуты
            important_attrs = ['href', 'src', 'alt', 'title']
            attrs_to_remove = []
            
            for attr in tag.attrs:
                if attr not in important_attrs:
                    attrs_to_remove.append(attr)
            
            for attr in attrs_to_remove:
                del tag[attr]
    
    def _remove_empty_elements(self, soup: BeautifulSoup):
        """Удаление пустых элементов"""
        # Элементы, которые могут быть пустыми по дизайну
        self_closing_tags = {'br', 'hr', 'img', 'input', 'meta', 'link'}
        
        # Несколько проходов для удаления вложенных пустых элементов
        for _ in range(3):
            empty_elements = []
            
            for tag in soup.find_all(True):
                if tag.name in self_closing_tags:
                    continue
                
                # Проверяем, пустой ли элемент
                if self._is_empty_element(tag):
                    empty_elements.append(tag)
            
            for element in empty_elements:
                element.decompose()
    
    def _is_empty_element(self, tag: Tag) -> bool:
        """Проверка, является ли элемент пустым"""
        # Получаем текст без дочерних тегов
        text = tag.get_text(strip=True)
        
        # Если есть текст, элемент не пустой
        if text:
            return False
        
        # Проверяем наличие важных дочерних элементов
        important_children = tag.find_all(['img', 'video', 'audio', 'iframe', 'canvas'])
        if important_children:
            return False
        
        return True
    
    def _normalize_text(self, soup: BeautifulSoup):
        """Нормализация текста в элементах"""
        for text_node in soup.find_all(string=True):
            if isinstance(text_node, NavigableString):
                cleaned_text = self._clean_text(str(text_node))
                if cleaned_text != str(text_node):
                    text_node.replace_with(cleaned_text)
    
    def _clean_text(self, text: str) -> str:
        """Очистка текста по паттернам"""
        for pattern, replacement in self.text_cleanup_patterns:
            text = re.sub(pattern, replacement, text)
        return text
    
    def extract_main_content(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Извлечение основного контента страницы"""
        # Поиск основного контента по селекторам
        main_selectors = [
            'main',
            '.main-content',
            '.content',
            '.article-content',
            '.documentation-content',
            '.page-content',
            '#content',
            '#main-content'
        ]
        
        for selector in main_selectors:
            main_content = soup.select_one(selector)
            if main_content:
                # Создаем новый документ только с основным контентом
                new_soup = BeautifulSoup('<html><body></body></html>', 'lxml')
                new_soup.body.append(main_content.extract())
                return new_soup
        
        # Если основной контент не найден, возвращаем очищенную версию
        return self.clean_html(soup)
    
    def remove_navigation_elements(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Специальная очистка навигационных элементов"""
        nav_patterns = [
            r'nav',
            r'navigation',
            r'menu',
            r'breadcrumb',
            r'pagination',
            r'next.*prev',
            r'table.*contents'
        ]
        
        for pattern in nav_patterns:
            # Поиск по классам и ID
            for attr in ['class', 'id']:
                elements = soup.find_all(attrs={attr: re.compile(pattern, re.I)})
                for element in elements:
                    element.decompose()
        
        return soup
    
    def preserve_code_blocks(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Сохранение блоков кода при очистке"""
        # Находим все блоки кода
        code_blocks = soup.find_all(['pre', 'code', '.code-listing', '.highlight'])
        
        # Сохраняем их содержимое
        preserved_code = {}
        for i, block in enumerate(code_blocks):
            placeholder = f"__CODE_BLOCK_{i}__"
            preserved_code[placeholder] = str(block)
            block.replace_with(placeholder)
        
        # Очищаем документ
        cleaned_soup = self.clean_html(soup)
        
        # Восстанавливаем блоки кода
        for placeholder, code_html in preserved_code.items():
            code_soup = BeautifulSoup(code_html, 'lxml')
            code_element = code_soup.find(['pre', 'code'])
            if code_element:
                # Находим placeholder в очищенном документе
                placeholder_text = cleaned_soup.find(string=placeholder)
                if placeholder_text:
                    placeholder_text.replace_with(code_element)
        
        return cleaned_soup
    
    def get_content_statistics(self, original_soup: BeautifulSoup, 
                             cleaned_soup: BeautifulSoup) -> dict:
        """Получение статистики очистки"""
        original_text = original_soup.get_text()
        cleaned_text = cleaned_soup.get_text()
        
        original_elements = len(original_soup.find_all(True))
        cleaned_elements = len(cleaned_soup.find_all(True))
        
        return {
            'original_text_length': len(original_text),
            'cleaned_text_length': len(cleaned_text),
            'text_reduction_percent': round((1 - len(cleaned_text) / len(original_text)) * 100, 2),
            'original_elements_count': original_elements,
            'cleaned_elements_count': cleaned_elements,
            'elements_reduction_percent': round((1 - cleaned_elements / original_elements) * 100, 2)
        }
