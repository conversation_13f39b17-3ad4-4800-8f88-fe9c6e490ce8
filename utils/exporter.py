"""
Модуль для экспорта данных в различные форматы
"""
import json
import asyncio
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
from slugify import slugify

import aiofiles

from models import DocumentationPage, ParseResult, Method, Property, CodeExample


class DataExporter:
    """Класс для экспорта данных парсинга"""
    
    def __init__(self, output_dir: Path):
        self.output_dir = Path(output_dir)
        self.markdown_dir = self.output_dir / "markdown"
        self.json_dir = self.output_dir / "json"
        
        # Создание директорий
        self.markdown_dir.mkdir(parents=True, exist_ok=True)
        self.json_dir.mkdir(parents=True, exist_ok=True)
    
    async def export_all(self, result: ParseResult) -> Dict[str, Any]:
        """Экспорт всех данных в различные форматы"""
        export_stats = {
            "total_pages": len(result.pages),
            "exported_markdown": 0,
            "exported_json": 0,
            "errors": []
        }
        
        # Экспорт отдельных страниц
        for page in result.pages:
            try:
                # Экспорт в Markdown
                await self.export_page_to_markdown(page)
                export_stats["exported_markdown"] += 1
                
                # Экспорт в JSON
                await self.export_page_to_json(page)
                export_stats["exported_json"] += 1
                
            except Exception as e:
                error_msg = f"Ошибка экспорта страницы {page.title}: {str(e)}"
                export_stats["errors"].append(error_msg)
        
        # Экспорт сводного отчета
        await self.export_summary_report(result)
        
        # Экспорт индекса всех страниц
        await self.export_index(result.pages)
        
        return export_stats
    
    async def export_page_to_markdown(self, page: DocumentationPage):
        """Экспорт страницы в Markdown формат"""
        filename = self._generate_filename(page.title, ".md")
        filepath = self.markdown_dir / filename
        
        markdown_content = self._generate_markdown_content(page)
        
        async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
            await f.write(markdown_content)
    
    async def export_page_to_json(self, page: DocumentationPage):
        """Экспорт страницы в JSON формат"""
        filename = self._generate_filename(page.title, ".json")
        filepath = self.json_dir / filename
        
        # Конвертация в словарь для JSON сериализации
        page_dict = page.model_dump()
        
        # Форматирование даты
        if 'parsed_at' in page_dict:
            page_dict['parsed_at'] = page_dict['parsed_at'].isoformat()
        
        async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(page_dict, indent=2, ensure_ascii=False))
    
    def _generate_markdown_content(self, page: DocumentationPage) -> str:
        """Генерация Markdown контента для страницы"""
        lines = []
        
        # Заголовок
        if page.class_name:
            lines.append(f"# Class: {page.class_name}")
        else:
            lines.append(f"# {page.title}")
        
        lines.append("")
        
        # Метаданные
        lines.append("## Metadata")
        lines.append(f"- **URL:** {page.url}")
        lines.append(f"- **Parsed at:** {page.parsed_at.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        # Описание
        if page.description:
            lines.append("## Description")
            lines.append(page.description)
            lines.append("")
        
        # Свойства
        if page.properties:
            lines.append("## Properties")
            lines.append("")
            for prop in page.properties:
                lines.extend(self._format_property_markdown(prop))
                lines.append("")
        
        # Методы
        if page.methods:
            lines.append("## Methods")
            lines.append("")
            for method in page.methods:
                lines.extend(self._format_method_markdown(method))
                lines.append("")
        
        # Примеры кода
        if page.examples:
            lines.append("## Code Examples")
            lines.append("")
            for example in page.examples:
                lines.extend(self._format_code_example_markdown(example))
                lines.append("")
        
        # Связанные ссылки
        if page.related_links:
            lines.append("## Related Links")
            lines.append("")
            for link in page.related_links[:10]:  # Ограничиваем количество ссылок
                lines.append(f"- [{link}]({link})")
            lines.append("")
        
        return "\n".join(lines)
    
    def _format_property_markdown(self, prop: Property) -> List[str]:
        """Форматирование свойства в Markdown"""
        lines = [f"### {prop.name}"]
        
        if prop.type:
            lines.append(f"- **Type:** `{prop.type}`")
        
        if prop.description:
            lines.append(f"- **Description:** {prop.description}")
        
        if prop.readonly:
            lines.append("- **Access:** Read-only")
        
        if prop.deprecated:
            lines.append("- **Status:** ⚠️ Deprecated")
        
        if prop.availability:
            lines.append(f"- **Availability:** {prop.availability}")
        
        return lines
    
    def _format_method_markdown(self, method: Method) -> List[str]:
        """Форматирование метода в Markdown"""
        lines = [f"### {method.name}"]
        
        if method.description:
            lines.append("")
            lines.append(method.description)
        
        # Параметры
        if method.parameters:
            lines.append("")
            lines.append("**Parameters:**")
            for param in method.parameters:
                param_line = f"- `{param.name}`"
                if param.type:
                    param_line += f" ({param.type})"
                if param.description:
                    param_line += f": {param.description}"
                if not param.required:
                    param_line += " *(optional)*"
                lines.append(param_line)
        
        # Возвращаемое значение
        if method.return_type:
            lines.append("")
            lines.append(f"**Returns:** `{method.return_type}`")
            if method.return_description:
                lines.append(f"- {method.return_description}")
        
        # Примеры кода для метода
        if method.examples:
            lines.append("")
            lines.append("**Examples:**")
            for example in method.examples:
                lines.extend(self._format_code_example_markdown(example))
        
        # Дополнительная информация
        if method.deprecated:
            lines.append("")
            lines.append("⚠️ **This method is deprecated**")
        
        if method.availability:
            lines.append(f"**Availability:** {method.availability}")
        
        return lines
    
    def _format_code_example_markdown(self, example: CodeExample) -> List[str]:
        """Форматирование примера кода в Markdown"""
        lines = []
        
        if example.description:
            lines.append(f"**{example.description}**")
            lines.append("")
        
        lines.append(f"```{example.language}")
        lines.append(example.code)
        lines.append("```")
        
        return lines
    
    def _generate_filename(self, title: str, extension: str) -> str:
        """Генерация имени файла из заголовка"""
        # Очистка и создание slug
        clean_title = slugify(title, max_length=50)
        if not clean_title:
            clean_title = "untitled"
        
        return f"{clean_title}{extension}"
    
    async def export_summary_report(self, result: ParseResult):
        """Экспорт сводного отчета"""
        report_lines = [
            "# Documentation Parsing Report",
            "",
            f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**Total Pages:** {result.total_pages}",
            f"**Successful:** {result.success_count}",
            f"**Errors:** {result.error_count}",
            ""
        ]
        
        if result.duration:
            report_lines.append(f"**Duration:** {result.duration:.2f} seconds")
            report_lines.append("")
        
        # Статистика по типам контента
        total_methods = sum(len(page.methods) for page in result.pages)
        total_properties = sum(len(page.properties) for page in result.pages)
        total_examples = sum(len(page.examples) for page in result.pages)
        
        report_lines.extend([
            "## Content Statistics",
            f"- **Methods:** {total_methods}",
            f"- **Properties:** {total_properties}",
            f"- **Code Examples:** {total_examples}",
            ""
        ])
        
        # Ошибки
        if result.errors:
            report_lines.extend([
                "## Errors",
                ""
            ])
            for error in result.errors:
                report_lines.append(f"- {error}")
            report_lines.append("")
        
        # Список страниц
        report_lines.extend([
            "## Parsed Pages",
            ""
        ])
        
        for page in result.pages:
            report_lines.append(f"- [{page.title}]({page.url})")
        
        # Сохранение отчета
        report_content = "\n".join(report_lines)
        
        async with aiofiles.open(self.output_dir / "parsing_report.md", 'w', encoding='utf-8') as f:
            await f.write(report_content)
        
        # JSON версия отчета
        report_data = {
            "generated_at": datetime.now().isoformat(),
            "total_pages": result.total_pages,
            "success_count": result.success_count,
            "error_count": result.error_count,
            "duration": result.duration,
            "statistics": {
                "total_methods": total_methods,
                "total_properties": total_properties,
                "total_examples": total_examples
            },
            "errors": result.errors,
            "pages": [{"title": page.title, "url": page.url} for page in result.pages]
        }
        
        async with aiofiles.open(self.output_dir / "parsing_report.json", 'w', encoding='utf-8') as f:
            await f.write(json.dumps(report_data, indent=2, ensure_ascii=False))
    
    async def export_index(self, pages: List[DocumentationPage]):
        """Экспорт индекса всех страниц"""
        index_lines = [
            "# Documentation Index",
            "",
            f"Total pages: {len(pages)}",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]
        
        # Группировка по типам
        classes = [p for p in pages if p.class_name]
        other_pages = [p for p in pages if not p.class_name]
        
        if classes:
            index_lines.extend([
                "## Classes and Protocols",
                ""
            ])
            for page in sorted(classes, key=lambda x: x.class_name or ""):
                methods_count = len(page.methods)
                properties_count = len(page.properties)
                index_lines.append(
                    f"- [{page.class_name}](./{self._generate_filename(page.title, '.md')}) "
                    f"({methods_count} methods, {properties_count} properties)"
                )
            index_lines.append("")
        
        if other_pages:
            index_lines.extend([
                "## Other Pages",
                ""
            ])
            for page in sorted(other_pages, key=lambda x: x.title):
                index_lines.append(f"- [{page.title}](./{self._generate_filename(page.title, '.md')})")
        
        index_content = "\n".join(index_lines)
        
        async with aiofiles.open(self.markdown_dir / "index.md", 'w', encoding='utf-8') as f:
            await f.write(index_content)
