"""
Модели данных для парсера документации
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, HttpUrl
from datetime import datetime


class Parameter(BaseModel):
    """Модель параметра метода"""
    name: str
    type: Optional[str] = None
    description: Optional[str] = None
    required: bool = True
    default_value: Optional[str] = None


class CodeExample(BaseModel):
    """Модель примера кода"""
    language: str
    code: str
    description: Optional[str] = None


class Method(BaseModel):
    """Модель метода или функции"""
    name: str
    description: Optional[str] = None
    parameters: List[Parameter] = []
    return_type: Optional[str] = None
    return_description: Optional[str] = None
    examples: List[CodeExample] = []
    availability: Optional[str] = None
    deprecated: bool = False


class Property(BaseModel):
    """Модель свойства"""
    name: str
    type: Optional[str] = None
    description: Optional[str] = None
    readonly: bool = False
    availability: Optional[str] = None
    deprecated: bool = False


class DocumentationPage(BaseModel):
    """Модель страницы документации"""
    url: str
    title: str
    description: Optional[str] = None
    class_name: Optional[str] = None
    methods: List[Method] = []
    properties: List[Property] = []
    examples: List[CodeExample] = []
    related_links: List[str] = []
    parsed_at: datetime = datetime.now()
    
    def to_markdown(self) -> str:
        """Конвертация в Markdown формат"""
        md_content = []
        
        # Заголовок
        if self.class_name:
            md_content.append(f"# Class: {self.class_name}")
        else:
            md_content.append(f"# {self.title}")
        
        # Описание
        if self.description:
            md_content.append(f"\n{self.description}\n")
        
        # Свойства
        if self.properties:
            md_content.append("## Properties")
            for prop in self.properties:
                md_content.append(f"### {prop.name}")
                if prop.type:
                    md_content.append(f"- **Type:** {prop.type}")
                if prop.description:
                    md_content.append(f"- **Description:** {prop.description}")
                if prop.readonly:
                    md_content.append("- **Read-only:** Yes")
                md_content.append("")
        
        # Методы
        if self.methods:
            md_content.append("## Methods")
            for method in self.methods:
                md_content.append(f"### {method.name}")
                if method.description:
                    md_content.append(f"{method.description}\n")
                
                if method.parameters:
                    md_content.append("**Parameters:**")
                    for param in method.parameters:
                        param_line = f"- `{param.name}`"
                        if param.type:
                            param_line += f" ({param.type})"
                        if param.description:
                            param_line += f": {param.description}"
                        md_content.append(param_line)
                    md_content.append("")
                
                if method.return_type:
                    md_content.append(f"**Returns:** {method.return_type}")
                    if method.return_description:
                        md_content.append(f"- {method.return_description}")
                    md_content.append("")
                
                # Примеры кода для метода
                for example in method.examples:
                    md_content.append(f"**Example ({example.language}):**")
                    md_content.append(f"```{example.language}")
                    md_content.append(example.code)
                    md_content.append("```")
                    if example.description:
                        md_content.append(f"*{example.description}*")
                    md_content.append("")
        
        # Общие примеры
        if self.examples:
            md_content.append("## Examples")
            for example in self.examples:
                if example.description:
                    md_content.append(f"### {example.description}")
                md_content.append(f"```{example.language}")
                md_content.append(example.code)
                md_content.append("```\n")
        
        # Связанные ссылки
        if self.related_links:
            md_content.append("## Related Links")
            for link in self.related_links:
                md_content.append(f"- {link}")
        
        return "\n".join(md_content)


class ParseResult(BaseModel):
    """Результат парсинга"""
    pages: List[DocumentationPage] = []
    total_pages: int = 0
    success_count: int = 0
    error_count: int = 0
    errors: List[str] = []
    start_time: datetime = datetime.now()
    end_time: Optional[datetime] = None
    
    @property
    def duration(self) -> Optional[float]:
        """Длительность парсинга в секундах"""
        if self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
