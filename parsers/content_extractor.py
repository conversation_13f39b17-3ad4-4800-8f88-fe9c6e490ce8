"""
Модуль для извлечения контента из страниц документации
"""
import re
from typing import List, Optional, Dict, Any
from bs4 import BeautifulSoup, Tag

from models import Method, Property, Parameter, CodeExample


class ContentExtractor:
    """Класс для извлечения структурированного контента"""
    
    def __init__(self):
        # Паттерны для распознавания типов
        self.type_patterns = {
            'swift': r'\b(String|Int|Bool|Double|Float|Array|Dictionary|Optional|UIView|NSObject)\b',
            'objc': r'\b(NSString|NSInteger|BOOL|CGFloat|NSArray|NSDictionary|nullable|nonnull)\b',
        }
        
        # Паттерны для параметров
        self.param_patterns = [
            r'(\w+):\s*([^,\)]+)',  # Swift style: param: Type
            r'(\w+)\s+([A-Z]\w+)',   # ObjC style: param Type
        ]
    
    def extract_methods(self, soup: BeautifulSoup) -> List[Method]:
        """Извлечение методов из страницы"""
        methods = []
        
        # Поиск различных селекторов для методов
        method_selectors = [
            '.method',
            '.function', 
            '.declaration',
            '[data-type="method"]',
            '.api-reference .method',
            '.symbol-declaration'
        ]
        
        for selector in method_selectors:
            method_elements = soup.select(selector)
            for elem in method_elements:
                method = self._parse_method_element(elem)
                if method:
                    methods.append(method)
        
        # Поиск методов в секциях
        method_sections = soup.find_all(['section', 'div'], 
                                       class_=re.compile(r'method|function|api'))
        for section in method_sections:
            method = self._parse_method_section(section)
            if method:
                methods.append(method)
        
        return self._deduplicate_methods(methods)
    
    def extract_properties(self, soup: BeautifulSoup) -> List[Property]:
        """Извлечение свойств из страницы"""
        properties = []
        
        # Поиск различных селекторов для свойств
        property_selectors = [
            '.property',
            '.variable',
            '[data-type="property"]',
            '.api-reference .property',
            '.symbol-property'
        ]
        
        for selector in property_selectors:
            prop_elements = soup.select(selector)
            for elem in prop_elements:
                prop = self._parse_property_element(elem)
                if prop:
                    properties.append(prop)
        
        return self._deduplicate_properties(properties)
    
    def extract_code_examples(self, soup: BeautifulSoup) -> List[CodeExample]:
        """Извлечение примеров кода"""
        examples = []
        
        # Поиск блоков кода
        code_selectors = [
            'pre code',
            '.code-listing',
            '.highlight',
            '.code-sample',
            '.example-code'
        ]
        
        for selector in code_selectors:
            code_elements = soup.select(selector)
            for elem in code_elements:
                example = self._parse_code_example(elem)
                if example:
                    examples.append(example)
        
        return examples
    
    def _parse_method_element(self, elem: Tag) -> Optional[Method]:
        """Парсинг элемента метода"""
        try:
            # Извлечение названия метода
            name = self._extract_method_name(elem)
            if not name:
                return None
            
            # Извлечение описания
            description = self._extract_description(elem)
            
            # Извлечение параметров
            parameters = self._extract_parameters(elem)
            
            # Извлечение типа возвращаемого значения
            return_type = self._extract_return_type(elem)
            
            # Извлечение примеров кода для метода
            examples = self._extract_method_examples(elem)
            
            # Проверка на deprecated
            deprecated = self._is_deprecated(elem)
            
            return Method(
                name=name,
                description=description,
                parameters=parameters,
                return_type=return_type,
                examples=examples,
                deprecated=deprecated
            )
        except Exception as e:
            print(f"Ошибка парсинга метода: {e}")
            return None
    
    def _parse_property_element(self, elem: Tag) -> Optional[Property]:
        """Парсинг элемента свойства"""
        try:
            # Извлечение названия свойства
            name = self._extract_property_name(elem)
            if not name:
                return None
            
            # Извлечение типа
            prop_type = self._extract_property_type(elem)
            
            # Извлечение описания
            description = self._extract_description(elem)
            
            # Проверка на readonly
            readonly = self._is_readonly(elem)
            
            # Проверка на deprecated
            deprecated = self._is_deprecated(elem)
            
            return Property(
                name=name,
                type=prop_type,
                description=description,
                readonly=readonly,
                deprecated=deprecated
            )
        except Exception as e:
            print(f"Ошибка парсинга свойства: {e}")
            return None
    
    def _parse_code_example(self, elem: Tag) -> Optional[CodeExample]:
        """Парсинг примера кода"""
        try:
            code = elem.get_text(strip=True)
            if not code or len(code) < 10:  # Игнорируем слишком короткие примеры
                return None
            
            # Определение языка
            language = self._detect_language(elem, code)
            
            # Поиск описания рядом с кодом
            description = self._find_code_description(elem)
            
            return CodeExample(
                language=language,
                code=code,
                description=description
            )
        except Exception as e:
            print(f"Ошибка парсинга примера кода: {e}")
            return None
    
    def _extract_method_name(self, elem: Tag) -> Optional[str]:
        """Извлечение названия метода"""
        # Поиск в различных местах
        selectors = [
            '.method-name',
            '.function-name', 
            '.symbol-name',
            'h3', 'h4', 'h5',
            '.title'
        ]
        
        for selector in selectors:
            name_elem = elem.select_one(selector)
            if name_elem:
                name = name_elem.get_text(strip=True)
                # Очистка от лишних символов
                name = re.sub(r'[^\w\(\):]+', '', name)
                if name and len(name) > 1:
                    return name
        
        return None
    
    def _extract_property_name(self, elem: Tag) -> Optional[str]:
        """Извлечение названия свойства"""
        selectors = [
            '.property-name',
            '.variable-name',
            '.symbol-name',
            'h3', 'h4', 'h5'
        ]
        
        for selector in selectors:
            name_elem = elem.select_one(selector)
            if name_elem:
                name = name_elem.get_text(strip=True)
                # Очистка от лишних символов
                name = re.sub(r'[^\w]+', '', name)
                if name and len(name) > 1:
                    return name
        
        return None
    
    def _extract_description(self, elem: Tag) -> Optional[str]:
        """Извлечение описания"""
        selectors = [
            '.description',
            '.summary',
            '.abstract',
            '.discussion',
            'p'
        ]
        
        for selector in selectors:
            desc_elem = elem.select_one(selector)
            if desc_elem:
                desc = desc_elem.get_text(strip=True)
                if desc and len(desc) > 10:
                    return desc
        
        return None
    
    def _extract_parameters(self, elem: Tag) -> List[Parameter]:
        """Извлечение параметров метода"""
        parameters = []
        
        # Поиск секции параметров
        param_sections = elem.select('.parameters, .params, .arguments')
        for section in param_sections:
            params = self._parse_parameter_section(section)
            parameters.extend(params)
        
        # Поиск отдельных параметров
        param_elements = elem.select('.parameter, .param, .argument')
        for param_elem in param_elements:
            param = self._parse_parameter_element(param_elem)
            if param:
                parameters.append(param)
        
        return parameters
    
    def _parse_parameter_section(self, section: Tag) -> List[Parameter]:
        """Парсинг секции параметров"""
        parameters = []
        
        # Поиск списков параметров
        param_items = section.select('li, .param-item, .parameter-item')
        for item in param_items:
            param = self._parse_parameter_element(item)
            if param:
                parameters.append(param)
        
        return parameters
    
    def _parse_parameter_element(self, elem: Tag) -> Optional[Parameter]:
        """Парсинг отдельного параметра"""
        try:
            text = elem.get_text(strip=True)
            
            # Попытка извлечь имя и тип параметра
            for pattern in self.param_patterns:
                match = re.search(pattern, text)
                if match:
                    name = match.group(1)
                    param_type = match.group(2) if len(match.groups()) > 1 else None
                    
                    # Извлечение описания
                    description = text.replace(match.group(0), '').strip()
                    if description.startswith(':') or description.startswith('-'):
                        description = description[1:].strip()
                    
                    return Parameter(
                        name=name,
                        type=param_type,
                        description=description if description else None
                    )
            
            return None
        except Exception as e:
            print(f"Ошибка парсинга параметра: {e}")
            return None
    
    def _extract_return_type(self, elem: Tag) -> Optional[str]:
        """Извлечение типа возвращаемого значения"""
        return_selectors = [
            '.return-type',
            '.returns',
            '.return-value'
        ]
        
        for selector in return_selectors:
            return_elem = elem.select_one(selector)
            if return_elem:
                return return_elem.get_text(strip=True)
        
        return None
    
    def _detect_language(self, elem: Tag, code: str) -> str:
        """Определение языка программирования"""
        # Проверка класса элемента
        classes = elem.get('class', [])
        for cls in classes:
            if 'swift' in cls.lower():
                return 'swift'
            elif 'objc' in cls.lower() or 'objective-c' in cls.lower():
                return 'objc'
            elif 'javascript' in cls.lower() or 'js' in cls.lower():
                return 'javascript'
        
        # Анализ содержимого кода
        if 'func ' in code or 'var ' in code or 'let ' in code:
            return 'swift'
        elif '@interface' in code or '@implementation' in code or '[' in code and ']' in code:
            return 'objc'
        elif 'function' in code or 'const ' in code or 'let ' in code:
            return 'javascript'
        
        return 'unknown'
    
    def _find_code_description(self, elem: Tag) -> Optional[str]:
        """Поиск описания для примера кода"""
        # Поиск в соседних элементах
        prev_elem = elem.find_previous_sibling()
        if prev_elem and prev_elem.name in ['p', 'div']:
            desc = prev_elem.get_text(strip=True)
            if desc and len(desc) < 200:  # Разумная длина для описания
                return desc
        
        return None
    
    def _is_deprecated(self, elem: Tag) -> bool:
        """Проверка на deprecated"""
        text = elem.get_text().lower()
        return 'deprecated' in text or 'obsolete' in text
    
    def _is_readonly(self, elem: Tag) -> bool:
        """Проверка на readonly свойство"""
        text = elem.get_text().lower()
        return 'readonly' in text or 'read-only' in text or 'get-only' in text
    
    def _extract_method_examples(self, elem: Tag) -> List[CodeExample]:
        """Извлечение примеров кода для конкретного метода"""
        examples = []
        
        # Поиск примеров в том же элементе
        code_elements = elem.select('pre code, .code-listing, .example')
        for code_elem in code_elements:
            example = self._parse_code_example(code_elem)
            if example:
                examples.append(example)
        
        return examples
    
    def _deduplicate_methods(self, methods: List[Method]) -> List[Method]:
        """Удаление дублирующихся методов"""
        seen = set()
        unique_methods = []
        
        for method in methods:
            if method.name not in seen:
                seen.add(method.name)
                unique_methods.append(method)
        
        return unique_methods
    
    def _deduplicate_properties(self, properties: List[Property]) -> List[Property]:
        """Удаление дублирующихся свойств"""
        seen = set()
        unique_properties = []
        
        for prop in properties:
            if prop.name not in seen:
                seen.add(prop.name)
                unique_properties.append(prop)
        
        return unique_properties
    
    def _parse_method_section(self, section: Tag) -> Optional[Method]:
        """Парсинг секции метода"""
        # Поиск заголовка секции
        header = section.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        if not header:
            return None
        
        name = header.get_text(strip=True)
        if not name:
            return None
        
        # Извлечение описания из секции
        description_elem = section.find(['p', '.description', '.summary'])
        description = description_elem.get_text(strip=True) if description_elem else None
        
        return Method(
            name=name,
            description=description
        )
