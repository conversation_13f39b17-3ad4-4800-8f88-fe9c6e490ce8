"""
Базовый класс для парсера документации
"""
import asyncio
import logging
import re
from typing import List, Optional, Set, Dict, Any
from urllib.parse import urljoin, urlparse
from pathlib import Path
from datetime import datetime

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from bs4 import BeautifulSoup
import aiofiles

from models import DocumentationPage, ParseResult, Method, Property, Parameter, CodeExample
from config import BROWSER_CONFIG, SELECTORS, URL_FILTERS, DELAY_BETWEEN_REQUESTS
from parsers.content_extractor import ContentExtractor
from utils.content_cleaner import ContentCleaner


class DocumentationParser:
    """Базовый класс для парсинга документации"""
    
    def __init__(self, base_url: str, start_url: str, max_depth: int = 3):
        self.base_url = base_url
        self.start_url = start_url
        self.max_depth = max_depth
        self.visited_urls: Set[str] = set()
        self.to_visit: List[tuple] = [(start_url, 0)]  # (url, depth)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None

        # Инициализация компонентов
        self.content_extractor = ContentExtractor()
        self.content_cleaner = ContentCleaner()

        # Настройка логирования
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)

        # Результаты парсинга
        self.result = ParseResult()
    
    async def __aenter__(self):
        """Асинхронный контекст-менеджер для инициализации браузера"""
        await self.init_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие браузера при выходе из контекста"""
        await self.close_browser()
    
    async def init_browser(self):
        """Инициализация браузера Playwright"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=BROWSER_CONFIG["headless"]
            )
            self.context = await self.browser.new_context(
                viewport=BROWSER_CONFIG["viewport"],
                user_agent=BROWSER_CONFIG["user_agent"]
            )
            self.logger.info("Браузер успешно инициализирован")
        except Exception as e:
            self.logger.error(f"Ошибка инициализации браузера: {e}")
            raise
    
    async def close_browser(self):
        """Закрытие браузера"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            self.logger.info("Браузер закрыт")
        except Exception as e:
            self.logger.error(f"Ошибка при закрытии браузера: {e}")
    
    def is_valid_url(self, url: str) -> bool:
        """Проверка валидности URL для парсинга"""
        if not url or url in self.visited_urls:
            return False
        
        # Проверка на принадлежность к базовому домену
        parsed_url = urlparse(url)
        base_parsed = urlparse(self.base_url)
        if parsed_url.netloc != base_parsed.netloc:
            return False
        
        # Проверка включающих паттернов
        include_patterns = URL_FILTERS.get("include_patterns", [])
        if include_patterns:
            if not any(re.search(pattern, url) for pattern in include_patterns):
                return False
        
        # Проверка исключающих паттернов
        exclude_patterns = URL_FILTERS.get("exclude_patterns", [])
        if any(re.search(pattern, url) for pattern in exclude_patterns):
            return False
        
        return True
    
    async def get_page_content(self, url: str) -> Optional[str]:
        """Получение содержимого страницы"""
        try:
            page = await self.context.new_page()
            await page.goto(url, wait_until="networkidle")
            
            # Ожидание загрузки JavaScript контента
            await page.wait_for_timeout(2000)
            
            content = await page.content()
            await page.close()
            
            return content
        except Exception as e:
            self.logger.error(f"Ошибка получения контента страницы {url}: {e}")
            return None
    
    def clean_content(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Очистка контента от ненужных элементов (устаревший метод)"""
        # Этот метод заменен на content_cleaner.clean_html()
        return self.content_cleaner.clean_html(soup)
    
    def extract_title(self, soup: BeautifulSoup) -> str:
        """Извлечение заголовка страницы"""
        for selector in SELECTORS["title"].split(", "):
            title_elem = soup.select_one(selector.strip())
            if title_elem:
                return title_elem.get_text(strip=True)
        
        # Fallback к title тегу
        title_tag = soup.find("title")
        if title_tag:
            return title_tag.get_text(strip=True)
        
        return "Untitled"
    
    def extract_description(self, soup: BeautifulSoup) -> Optional[str]:
        """Извлечение описания страницы"""
        for selector in SELECTORS["description"].split(", "):
            desc_elem = soup.select_one(selector.strip())
            if desc_elem:
                return desc_elem.get_text(strip=True)
        return None
    
    def extract_navigation_links(self, soup: BeautifulSoup, current_url: str) -> List[str]:
        """Извлечение ссылок для дальнейшего парсинга"""
        links = []
        for link in soup.select(SELECTORS["navigation_links"]):
            href = link.get("href")
            if href:
                full_url = urljoin(current_url, href)
                if self.is_valid_url(full_url):
                    links.append(full_url)
        return links
    
    async def parse_page(self, url: str) -> Optional[DocumentationPage]:
        """Парсинг отдельной страницы"""
        try:
            self.logger.info(f"Парсинг страницы: {url}")

            content = await self.get_page_content(url)
            if not content:
                return None

            soup = BeautifulSoup(content, 'lxml')

            # Очистка контента
            soup = self.content_cleaner.clean_html(soup)
            soup = self.content_cleaner.extract_main_content(soup)

            # Извлечение базовой информации
            title = self.extract_title(soup)
            description = self.extract_description(soup)

            # Создание объекта страницы
            page = DocumentationPage(
                url=url,
                title=title,
                description=description
            )

            # Извлечение класса из заголовка (если есть)
            if "class" in title.lower() or "protocol" in title.lower():
                page.class_name = title.split(":")[-1].strip() if ":" in title else title

            # Извлечение структурированного контента
            page.methods = self.content_extractor.extract_methods(soup)
            page.properties = self.content_extractor.extract_properties(soup)
            page.examples = self.content_extractor.extract_code_examples(soup)

            # Извлечение связанных ссылок
            page.related_links = self.extract_navigation_links(soup, url)

            self.logger.info(f"Успешно спарсена страница: {title}")
            return page

        except Exception as e:
            self.logger.error(f"Ошибка парсинга страницы {url}: {e}")
            self.result.errors.append(f"Ошибка парсинга {url}: {str(e)}")
            return None

    async def crawl_recursively(self) -> ParseResult:
        """Рекурсивный обход страниц документации"""
        self.result.start_time = datetime.now()

        try:
            while self.to_visit:
                url, depth = self.to_visit.pop(0)

                # Проверка глубины
                if depth > self.max_depth:
                    continue

                # Проверка, была ли страница уже посещена
                if url in self.visited_urls:
                    continue

                self.visited_urls.add(url)

                # Парсинг страницы
                page = await self.parse_page(url)
                if page:
                    self.result.pages.append(page)
                    self.result.success_count += 1

                    # Добавление новых ссылок для обхода
                    await self.add_links_to_queue(page.related_links, depth + 1)
                else:
                    self.result.error_count += 1

                # Задержка между запросами
                await asyncio.sleep(DELAY_BETWEEN_REQUESTS)

                self.logger.info(f"Обработано: {len(self.visited_urls)}, "
                               f"Осталось: {len(self.to_visit)}, "
                               f"Глубина: {depth}")

        except Exception as e:
            self.logger.error(f"Ошибка при рекурсивном обходе: {e}")
            self.result.errors.append(f"Критическая ошибка: {str(e)}")

        finally:
            self.result.end_time = datetime.now()
            self.result.total_pages = len(self.result.pages)

        return self.result

    async def add_links_to_queue(self, links: List[str], depth: int):
        """Добавление ссылок в очередь для обхода"""
        for link in links:
            if self.is_valid_url(link) and link not in self.visited_urls:
                # Проверяем, нет ли уже в очереди
                if not any(url == link for url, _ in self.to_visit):
                    self.to_visit.append((link, depth))

    async def parse_with_concurrency(self, max_concurrent: int = 5) -> ParseResult:
        """Парсинг с ограниченным количеством одновременных запросов"""
        semaphore = asyncio.Semaphore(max_concurrent)
        self.result.start_time = datetime.now()

        async def parse_with_semaphore(url: str, depth: int):
            async with semaphore:
                if url in self.visited_urls or depth > self.max_depth:
                    return None

                self.visited_urls.add(url)
                page = await self.parse_page(url)

                if page:
                    self.result.success_count += 1
                    return page
                else:
                    self.result.error_count += 1
                    return None

        try:
            # Первый проход - парсим начальные страницы
            tasks = []
            initial_urls = self.to_visit.copy()

            for url, depth in initial_urls:
                task = parse_with_semaphore(url, depth)
                tasks.append(task)

            # Выполняем задачи
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Обрабатываем результаты
            for result in results:
                if isinstance(result, DocumentationPage):
                    self.result.pages.append(result)
                elif isinstance(result, Exception):
                    self.result.errors.append(str(result))

        except Exception as e:
            self.logger.error(f"Ошибка при параллельном парсинге: {e}")
            self.result.errors.append(f"Критическая ошибка: {str(e)}")

        finally:
            self.result.end_time = datetime.now()
            self.result.total_pages = len(self.result.pages)

        return self.result
