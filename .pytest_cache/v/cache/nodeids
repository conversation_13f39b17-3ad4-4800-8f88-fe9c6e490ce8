["tests/test_parser.py::TestContentCleaner::test_remove_empty_elements", "tests/test_parser.py::TestContentCleaner::test_remove_navigation_elements", "tests/test_parser.py::TestContentExtractor::test_extract_code_examples", "tests/test_parser.py::TestContentExtractor::test_extract_methods_from_html", "tests/test_parser.py::TestContentExtractor::test_extract_properties_from_html", "tests/test_parser.py::TestDataExporter::test_export_page_to_json", "tests/test_parser.py::TestDataExporter::test_export_page_to_markdown", "tests/test_parser.py::TestDocumentationParser::test_title_extraction", "tests/test_parser.py::TestDocumentationParser::test_url_validation", "tests/test_parser.py::test_models_validation"]