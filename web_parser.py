#!/usr/bin/env python3
"""
Специализированный парсер для веб-сайтов
"""
import asyncio
import logging
from pathlib import Path

import click
from colorama import init, Fore, Style

# Инициализация colorama
init()

from parsers.base_parser import DocumentationParser
from utils.exporter import DataExporter
import config_web as config


def setup_logging(verbose: bool = False):
    """Настройка логирования"""
    level = logging.DEBUG if verbose else logging.INFO
    
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    log_file = config.OUTPUT_DIR / "logs" / "web_parser.log"
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)


def print_banner():
    """Вывод баннера приложения"""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                      Web Site Parser                        ║
║                                                              ║
║  Парсер веб-сайтов с поддержкой JavaScript                  ║
║  Экспорт контента в Markdown и JSON                         ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(banner)


async def parse_website(url, base_url, max_depth, output, delay, concurrent, mode, verbose):
    """Парсинг веб-сайта"""
    setup_logging(verbose)
    print_banner()
    
    logger = logging.getLogger(__name__)
    logger.info(f"Начало парсинга веб-сайта: {url}")
    
    output_path = Path(output)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"{Fore.GREEN}🚀 Запуск парсера веб-сайта...{Style.RESET_ALL}")
    print(f"📍 URL: {url}")
    print(f"🏠 Базовый URL: {base_url}")
    print(f"📊 Максимальная глубина: {max_depth}")
    print(f"💾 Директория вывода: {output_path}")
    print(f"⏱️  Задержка: {delay}с")
    print(f"🔄 Режим: {mode}")
    print()
    
    try:
        # Создаем парсер с веб-конфигурацией
        async with DocumentationParser(base_url, url, max_depth) as parser:
            # Обновляем конфигурацию для веб-сайтов
            parser.delay = delay
            
            # Обновляем фильтры URL
            parser.url_filters = config.URL_FILTERS
            
            print(f"{Fore.YELLOW}🔄 Парсинг в режиме: {mode}...{Style.RESET_ALL}")
            
            if mode == 'concurrent':
                result = await parser.parse_with_concurrency(concurrent)
            else:
                result = await parser.crawl_recursively()
            
            # Статистика
            print(f"\n{Fore.GREEN}✅ Парсинг завершен!{Style.RESET_ALL}")
            print(f"📄 Обработано страниц: {result.total_pages}")
            print(f"✅ Успешно: {result.success_count}")
            print(f"❌ Ошибок: {result.error_count}")
            
            if result.duration:
                print(f"⏱️  Время выполнения: {result.duration:.2f} секунд")
            
            # Показ найденного контента
            if result.pages:
                print(f"\n{Fore.CYAN}📋 Найденный контент:{Style.RESET_ALL}")
                for i, page in enumerate(result.pages[:5], 1):  # Показываем первые 5 страниц
                    print(f"   {i}. {page.title} ({page.url})")
                    if page.description:
                        print(f"      📝 {page.description[:100]}...")
                    print(f"      🔗 Ссылок: {len(page.related_links)}")
                    print(f"      💻 Примеров: {len(page.examples)}")
                    print()
            
            # Экспорт
            print(f"{Fore.CYAN}💾 Экспорт результатов...{Style.RESET_ALL}")
            exporter = DataExporter(output_path)
            export_stats = await exporter.export_all(result)
            
            print(f"📝 Markdown файлов: {export_stats['exported_markdown']}")
            print(f"📋 JSON файлов: {export_stats['exported_json']}")
            
            if export_stats['errors']:
                print(f"{Fore.RED}⚠️  Ошибки экспорта: {len(export_stats['errors'])}{Style.RESET_ALL}")
            
            print(f"\n{Fore.GREEN}📁 Результаты сохранены в:{Style.RESET_ALL}")
            print(f"   📝 Markdown: {output_path / 'markdown'}")
            print(f"   📋 JSON: {output_path / 'json'}")
            print(f"   📊 Отчеты: {output_path}")
            
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}⏹️  Парсинг прерван пользователем{Style.RESET_ALL}")
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        print(f"\n{Fore.RED}💥 Ошибка: {e}{Style.RESET_ALL}")


def main():
    """Главная функция"""
    # Создаем синхронную обертку для click
    @click.command()
    @click.option('--url', '-u', default=config.START_URL, help='URL для парсинга')
    @click.option('--base-url', '-b', default=config.BASE_URL, help='Базовый URL сайта')
    @click.option('--max-depth', '-d', default=config.MAX_DEPTH, help='Максимальная глубина обхода')
    @click.option('--output', '-o', default=str(config.OUTPUT_DIR), help='Директория для сохранения')
    @click.option('--delay', default=config.DELAY_BETWEEN_REQUESTS, help='Задержка между запросами (сек)')
    @click.option('--concurrent', '-c', default=config.MAX_CONCURRENT_REQUESTS, help='Количество одновременных запросов')
    @click.option('--mode', type=click.Choice(['sequential', 'concurrent']), default='sequential', help='Режим парсинга')
    @click.option('--verbose', '-v', is_flag=True, help='Подробный вывод')
    def sync_parse_website(url, base_url, max_depth, output, delay, concurrent, mode, verbose):
        """Синхронная обертка для парсинга"""
        asyncio.run(parse_website(url, base_url, max_depth, output, delay, concurrent, mode, verbose))

    sync_parse_website()


if __name__ == "__main__":
    main()
