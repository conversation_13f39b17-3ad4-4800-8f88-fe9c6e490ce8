"""
Конфигурация для парсинга обычных веб-сайтов
"""
import os
from pathlib import Path

# Базовые настройки для веб-сайтов
BASE_URL = "https://www.augmentcode.com"
START_URL = "https://www.augmentcode.com/"

# Настройки парсера
MAX_DEPTH = 2  # Максимальная глубина рекурсивного обхода
DELAY_BETWEEN_REQUESTS = 1  # Задержка между запросами в секундах
MAX_CONCURRENT_REQUESTS = 3  # Максимальное количество одновременных запросов

# Пути для сохранения
OUTPUT_DIR = Path("output_web")
MARKDOWN_DIR = OUTPUT_DIR / "markdown"
JSON_DIR = OUTPUT_DIR / "json"
LOGS_DIR = OUTPUT_DIR / "logs"

# Создание директорий
for dir_path in [OUTPUT_DIR, MARKDOWN_DIR, JSON_DIR, LOGS_DIR]:
    dir_path.mkdir(exist_ok=True)

# Настройки браузера
BROWSER_CONFIG = {
    "headless": True,
    "viewport": {"width": 1920, "height": 1080},
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}

# Селекторы для извлечения контента с веб-сайтов
SELECTORS = {
    "title": "h1, h2, title, .title, .heading, .hero-title",
    "description": "p, .description, .summary, .intro, .lead, .subtitle",
    "methods": ".method, .function, .api, .endpoint",
    "parameters": ".parameter, .param, .argument, .field",
    "code_examples": "pre, code, .code, .highlight, .code-block",
    "navigation_links": "a[href]",  # Все ссылки
    "content_area": "main, .main, .content, .container, article, .article, body",
    "remove_elements": [
        # Навигация
        "nav", ".nav", ".navigation", ".navbar", ".menu",
        # Служебные элементы
        "header", "footer", ".header", ".footer",
        # Боковые панели
        ".sidebar", ".aside", "aside",
        # Реклама и промо
        ".ad", ".ads", ".advertisement", ".promo", ".banner",
        # Социальные сети
        ".social", ".share", ".sharing",
        # Формы и интерактивные элементы
        "form", ".form", ".subscribe", ".newsletter",
        # Метаданные
        ".breadcrumb", ".breadcrumbs", ".tags", ".metadata",
        # Уведомления
        ".alert", ".notice", ".notification", ".cookie",
        # Скрипты и стили
        "script", "style", "noscript"
    ]
}

# Фильтры URL для веб-сайтов
URL_FILTERS = {
    "include_patterns": [
        r"^https://www\.augmentcode\.com",  # Только домен Augment Code
    ],
    "exclude_patterns": [
        r"\.pdf$",
        r"\.jpg$", r"\.jpeg$", r"\.png$", r"\.gif$",  # Изображения
        r"\.css$", r"\.js$",  # Стили и скрипты
        r"mailto:",  # Email ссылки
        r"tel:",     # Телефонные ссылки
        r"#",        # Якорные ссылки
        r"/admin/",  # Админ панели
        r"/login",   # Страницы входа
        r"/register", # Страницы регистрации
    ]
}

# Специальные селекторы для разных типов контента
CONTENT_SELECTORS = {
    "headings": "h1, h2, h3, h4, h5, h6",
    "paragraphs": "p",
    "lists": "ul, ol, li",
    "links": "a[href]",
    "images": "img[src]",
    "videos": "video, iframe[src*='youtube'], iframe[src*='vimeo']",
    "buttons": "button, .button, .btn, input[type='submit']",
    "features": ".feature, .benefit, .advantage, .service",
    "testimonials": ".testimonial, .review, .quote",
    "pricing": ".price, .pricing, .plan, .package",
    "contact": ".contact, .email, .phone, .address"
}
